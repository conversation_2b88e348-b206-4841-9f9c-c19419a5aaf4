import 'package:flutter/material.dart';
import '../../shared/theme.dart';

class SearchFilterSortWidget extends StatefulWidget {
  final TextEditingController searchController;
  final Function(String) onSearchChanged;
  final VoidCallback onSearchClear;
  final String searchHintText;
  final String listTitle;
  final String? selectedItemText;

  // Sort functionality
  final String currentSortBy;
  final Function(String) onSortChanged;
  final List<SortOption> sortOptions;

  // Filter functionality (optional)
  final String? currentFilterBy;
  final Function(String)? onFilterChanged;
  final List<FilterOption>? filterOptions;

  // Search and filter results info
  final String searchResult;
  final int resultCount;

  const SearchFilterSortWidget({
    super.key,
    required this.searchController,
    required this.onSearchChanged,
    required this.onSearchClear,
    required this.searchHintText,
    required this.listTitle,
    this.selectedItemText,
    required this.currentSortBy,
    required this.onSortChanged,
    required this.sortOptions,
    this.currentFilterBy,
    this.onFilterChanged,
    this.filterOptions,
    required this.searchResult,
    required this.resultCount,
  });

  @override
  State<SearchFilterSortWidget> createState() => _SearchFilterSortWidgetState();
}

class _SearchFilterSortWidgetState extends State<SearchFilterSortWidget> {
  @override
  void initState() {
    super.initState();
    widget.searchController.addListener(() {
      setState(() {});
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 20),

        // Search Bar
        _buildSearchBar(),

        // Title and Controls Row
        _buildTitleAndControls(),

        const SizedBox(height: 15),

        // Search and filter results info
        _buildResultsInfo(),
      ],
    );
  }

  Widget _buildSearchBar() {
    return Container(
      margin: const EdgeInsets.only(bottom: 20),
      decoration: BoxDecoration(
        color: lightGreyColor,
        borderRadius: BorderRadius.circular(14),
      ),
      child: TextField(
        controller: widget.searchController,
        onChanged: widget.onSearchChanged,
        decoration: InputDecoration(
          hintText: widget.searchHintText,
          hintStyle: greyTextStyle.copyWith(fontSize: 14),
          prefixIcon: Icon(
            Icons.search,
            color: greyColor,
          ),
          suffixIcon: widget.searchController.text.isNotEmpty
              ? IconButton(
                  icon: Icon(Icons.clear, color: greyColor),
                  onPressed: widget.onSearchClear,
                )
              : null,
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 16,
          ),
        ),
      ),
    );
  }

  Widget _buildTitleAndControls() {
    return Row(
      children: [
        Text(
          widget.listTitle,
          style: blackTextStyle.copyWith(
            fontSize: 16,
            fontWeight: semiBold,
          ),
        ),
        const Spacer(),
        if (widget.selectedItemText != null)
          Text(
            widget.selectedItemText!,
            style: greyTextStyle.copyWith(
              fontSize: 12,
              fontStyle: FontStyle.italic,
            ),
          )
        else
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Filter button (if filter options provided)
              if (widget.filterOptions != null &&
                  widget.onFilterChanged != null)
                IconButton(
                  onPressed: _showFilterOptions,
                  icon: Icon(
                    Icons.filter_list,
                    color: (widget.currentFilterBy != null &&
                            widget.currentFilterBy != 'all')
                        ? redColor
                        : greyColor,
                    size: 20,
                  ),
                  tooltip: 'Filter ${widget.listTitle.toLowerCase()}',
                ),

              // Sort button
              IconButton(
                onPressed: _showSortOptions,
                icon: Icon(
                  Icons.sort,
                  color: greyColor,
                  size: 20,
                ),
                tooltip: 'Sort ${widget.listTitle.toLowerCase()}',
              ),
            ],
          ),
      ],
    );
  }

  Widget _buildResultsInfo() {
    if (widget.searchResult.isEmpty &&
        (widget.currentFilterBy == null || widget.currentFilterBy == 'all')) {
      return const SizedBox.shrink();
    }

    return Padding(
      padding: const EdgeInsets.only(bottom: 10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (widget.searchResult.isNotEmpty)
            Text(
              'Search: "${widget.searchResult}"',
              style: greyTextStyle.copyWith(
                fontSize: 12,
                fontStyle: FontStyle.italic,
              ),
            ),
          if (widget.currentFilterBy != null && widget.currentFilterBy != 'all')
            Text(
              'Filter: ${_getFilterDisplayName(widget.currentFilterBy!)}',
              style: greyTextStyle.copyWith(
                fontSize: 12,
                fontStyle: FontStyle.italic,
              ),
            ),
          Text(
            'Found ${widget.resultCount} result(s)',
            style: greyTextStyle.copyWith(
              fontSize: 12,
              fontWeight: medium,
            ),
          ),
        ],
      ),
    );
  }

  String _getFilterDisplayName(String filter) {
    final filterOption = widget.filterOptions?.firstWhere(
      (option) => option.value == filter,
      orElse: () =>
          FilterOption(value: filter, label: filter, icon: Icons.list),
    );
    return filterOption?.label ?? filter;
  }

  void _showSortOptions() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (BuildContext context) {
        return Container(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Sort ${widget.listTitle}',
                style: blackTextStyle.copyWith(
                  fontSize: 18,
                  fontWeight: semiBold,
                ),
              ),
              const SizedBox(height: 20),
              ...widget.sortOptions.map((option) => ListTile(
                    leading: Icon(
                      option.icon,
                      color: widget.currentSortBy == option.value
                          ? redColor
                          : greyColor,
                    ),
                    title: Text(
                      option.label,
                      style: blackTextStyle.copyWith(
                        fontWeight: widget.currentSortBy == option.value
                            ? semiBold
                            : medium,
                      ),
                    ),
                    trailing: widget.currentSortBy == option.value
                        ? Icon(Icons.check, color: redColor)
                        : null,
                    onTap: () {
                      widget.onSortChanged(option.value);
                      Navigator.pop(context);
                    },
                  )),
              const SizedBox(height: 20),
            ],
          ),
        );
      },
    );
  }

  void _showFilterOptions() {
    if (widget.filterOptions == null || widget.onFilterChanged == null) return;

    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (BuildContext context) {
        return Container(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Filter ${widget.listTitle}',
                style: blackTextStyle.copyWith(
                  fontSize: 18,
                  fontWeight: semiBold,
                ),
              ),
              const SizedBox(height: 20),
              ...widget.filterOptions!.map((option) => ListTile(
                    leading: Icon(
                      option.icon,
                      color: widget.currentFilterBy == option.value
                          ? redColor
                          : greyColor,
                    ),
                    title: Text(
                      option.label,
                      style: blackTextStyle.copyWith(
                        fontWeight: widget.currentFilterBy == option.value
                            ? semiBold
                            : medium,
                      ),
                    ),
                    trailing: widget.currentFilterBy == option.value
                        ? Icon(Icons.check, color: redColor)
                        : null,
                    onTap: () {
                      widget.onFilterChanged!(option.value);
                      Navigator.pop(context);
                    },
                  )),
              const SizedBox(height: 20),
            ],
          ),
        );
      },
    );
  }
}

class SortOption {
  final String value;
  final String label;
  final IconData icon;

  const SortOption({
    required this.value,
    required this.label,
    required this.icon,
  });
}

class FilterOption {
  final String value;
  final String label;
  final IconData icon;

  const FilterOption({
    required this.value,
    required this.label,
    required this.icon,
  });
}
